import { DBOS, WorkflowQueue } from "@dbos-inc/dbos-sdk";
import express, { Request, Response, NextFunction } from "express";
import path from 'path';
import fs from 'fs';

// Types and Interfaces
interface ComplianceDocument {
  id: string;
  content: string;
  documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
  uploadedAt: Date;
  status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}

interface ComplianceRule {
  id: string;
  standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
  ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
  description: string;
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface KYCProfile {
  customerId: string;
  personalInfo: {
    name: string;
    dateOfBirth: string;
    ssn: string;
    address: string;
  };
  riskScore: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  lastUpdated: Date;
}

interface ComplianceViolation {
  documentId: string;
  ruleId: string;
  violationType: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction: string;
  detectedAt: Date;
}

interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

// Express app setup
export const app = express();
app.use(express.json());

// CORS middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Queues for different compliance processes
const complianceQueue = new WorkflowQueue("compliance_checks", { 
  concurrency: 5, 
  rateLimit: { limitPerPeriod: 100, periodSec: 60 } 
});

const kycQueue = new WorkflowQueue("kyc_processing", { 
  concurrency: 3,
  rateLimit: { limitPerPeriod: 50, periodSec: 60 }
});

const reportingQueue = new WorkflowQueue("report_generation", { 
  concurrency: 2 
});

// Database transaction methods for compliance rules
export class ComplianceDatabase {

  @DBOS.transaction()
  static async getActiveComplianceRules(): Promise<ComplianceRule[]> {
    const result = await DBOS.pgClient.query(`
      SELECT rule_id as id, standard, rule_type as "ruleType", description, pattern, severity
      FROM compliance_rules
      WHERE is_active = true
      ORDER BY severity DESC, standard ASC
    `);

    return result.rows.map((row: any) => ({
      id: row.id,
      standard: row.standard,
      ruleType: row.ruleType,
      description: row.description,
      pattern: row.pattern,
      severity: row.severity
    }));
  }

  @DBOS.transaction()
  static async saveDocument(document: ComplianceDocument): Promise<string> {
    const result = await DBOS.pgClient.query(`
      INSERT INTO compliance_documents (document_id, content, document_type, status, file_name, uploaded_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (document_id) DO UPDATE SET
        content = EXCLUDED.content,
        status = EXCLUDED.status,
        updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `, [document.id, document.content, document.documentType, document.status,
        document.id + '.pdf', 'system']);

    return result.rows[0].id;
  }

  @DBOS.transaction()
  static async saveViolations(violations: ComplianceViolation[]): Promise<void> {
    if (violations.length === 0) return;

    const values = violations.map((_v, index) =>
      `($${index * 6 + 1}, $${index * 6 + 2}, $${index * 6 + 3}, $${index * 6 + 4}, $${index * 6 + 5}, $${index * 6 + 6})`
    ).join(', ');

    const params = violations.flatMap(v => [
      v.documentId, v.ruleId, v.violationType, v.description, v.severity, v.recommendedAction
    ]);

    await DBOS.pgClient.query(`
      INSERT INTO compliance_violations (document_id, rule_id, violation_type, description, severity, recommended_action)
      VALUES ${values}
    `, params);
  }

  @DBOS.transaction()
  static async saveKYCProfile(profile: KYCProfile): Promise<string> {
    const result = await DBOS.pgClient.query(`
      INSERT INTO kyc_profiles (customer_id, personal_info, risk_score, status)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (customer_id) DO UPDATE SET
        personal_info = EXCLUDED.personal_info,
        risk_score = EXCLUDED.risk_score,
        status = EXCLUDED.status,
        updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `, [profile.customerId, JSON.stringify(profile.personalInfo), profile.riskScore, profile.status]);

    return result.rows[0].id;
  }

  @DBOS.transaction()
  static async getComplianceMetrics(): Promise<{
    totalDocuments: number;
    compliantDocuments: number;
    violationsCount: number;
    complianceRate: number;
  }> {
    const metricsResult = await DBOS.pgClient.query(`
      SELECT
        COUNT(*) as total_documents,
        COUNT(CASE WHEN status = 'compliant' THEN 1 END) as compliant_documents,
        (SELECT COUNT(*) FROM compliance_violations WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as violations_count
      FROM compliance_documents
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `);

    const row = metricsResult.rows[0];
    const totalDocuments = parseInt(row.total_documents) || 0;
    const compliantDocuments = parseInt(row.compliant_documents) || 0;
    const violationsCount = parseInt(row.violations_count) || 0;
    const complianceRate = totalDocuments > 0 ? (compliantDocuments / totalDocuments) * 100 : 100;

    return {
      totalDocuments,
      compliantDocuments,
      violationsCount,
      complianceRate
    };
  }

  @DBOS.transaction()
  static async saveComplianceReport(report: ComplianceReport): Promise<string> {
    const result = await DBOS.pgClient.query(`
      INSERT INTO compliance_reports (report_id, report_type, compliance_rate, recommendations, report_data)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `, [report.id, report.reportType, report.compliance_rate, report.recommendations, JSON.stringify(report)]);

    return result.rows[0].id;
  }

  @DBOS.transaction()
  static async getRegulatoryUpdates(): Promise<RegulatoryUpdate[]> {
    const result = await DBOS.pgClient.query(`
      SELECT update_id as id, standard, title, description, effective_date as "effectiveDate",
             impact, action_required as "actionRequired"
      FROM regulatory_updates
      WHERE effective_date >= CURRENT_DATE - INTERVAL '90 days'
      ORDER BY effective_date DESC, impact DESC
      LIMIT 10
    `);

    return result.rows.map((row: any) => ({
      id: row.id,
      standard: row.standard,
      title: row.title,
      description: row.description,
      effectiveDate: row.effectiveDate,
      impact: row.impact,
      actionRequired: row.actionRequired
    }));
  }

  @DBOS.transaction()
  static async getDashboardMetrics(): Promise<{
    complianceRate: number;
    activeViolations: number;
    pendingKYC: number;
    completedReports: number;
    regulatoryUpdates: number;
  }> {
    const metricsResult = await DBOS.pgClient.query(`
      SELECT
        (SELECT ROUND(AVG(CASE WHEN status = 'compliant' THEN 100.0 ELSE 0.0 END), 1)
         FROM compliance_documents WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as compliance_rate,
        (SELECT COUNT(*) FROM compliance_violations WHERE severity IN ('critical', 'high')
         AND created_at >= CURRENT_DATE - INTERVAL '7 days') as active_violations,
        (SELECT COUNT(*) FROM kyc_profiles WHERE status = 'pending') as pending_kyc,
        (SELECT COUNT(*) FROM compliance_reports WHERE generated_at >= CURRENT_DATE - INTERVAL '30 days') as completed_reports,
        (SELECT COUNT(*) FROM regulatory_updates WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as regulatory_updates
    `);

    const row = metricsResult.rows[0];
    return {
      complianceRate: parseFloat(row.compliance_rate) || 98.2,
      activeViolations: parseInt(row.active_violations) || 0,
      pendingKYC: parseInt(row.pending_kyc) || 0,
      completedReports: parseInt(row.completed_reports) || 0,
      regulatoryUpdates: parseInt(row.regulatory_updates) || 0
    };
  }

  @DBOS.transaction()
  static async getRecentDocuments(): Promise<any[]> {
    const result = await DBOS.pgClient.query(`
      SELECT
        d.document_id as id,
        d.file_name as name,
        CASE
          WHEN d.file_size IS NOT NULL THEN ROUND(d.file_size / 1024.0 / 1024.0, 1) || ' MB'
          ELSE '1.2 MB'
        END as size,
        CASE
          WHEN d.status = 'compliant' THEN 'Compliant'
          WHEN d.status = 'non_compliant' THEN 'Violation Detected'
          WHEN d.status = 'processing' THEN 'Processing'
          ELSE 'Under Review'
        END as status,
        COALESCE((SELECT COUNT(*) FROM compliance_violations v WHERE v.document_id = d.document_id), 0) as violations,
        TO_CHAR(d.created_at, 'YYYY-MM-DD HH24:MI') as "uploadDate"
      FROM compliance_documents d
      ORDER BY d.created_at DESC
      LIMIT 10
    `);

    return result.rows.map((row: any) => ({
      id: parseInt(row.id.split('-')[1]) || Math.floor(Math.random() * 1000),
      name: row.name || `Document_${row.id}.pdf`,
      size: row.size,
      status: row.status,
      violations: parseInt(row.violations),
      uploadDate: row.uploadDate,
      complianceChecks: ['SEC', 'SOX', 'GLBA'] // Default compliance checks
    }));
  }

  @DBOS.transaction()
  static async getRecentViolations(): Promise<any[]> {
    const result = await DBOS.pgClient.query(`
      SELECT
        v.id,
        d.file_name as document,
        v.description as violation,
        CASE
          WHEN v.severity = 'critical' THEN 'Critical'
          WHEN v.severity = 'high' THEN 'High'
          WHEN v.severity = 'medium' THEN 'Medium'
          ELSE 'Low'
        END as severity,
        TO_CHAR(v.created_at, 'YYYY-MM-DD') as date,
        'Under Review' as status
      FROM compliance_violations v
      JOIN compliance_documents d ON v.document_id = d.document_id
      ORDER BY v.created_at DESC, v.severity DESC
      LIMIT 10
    `);

    return result.rows.map((row: any, index: number) => ({
      id: index + 1,
      document: row.document || 'Unknown Document',
      violation: row.violation,
      severity: row.severity,
      date: row.date,
      status: row.status
    }));
  }
}

export class ComplianceSystem {
  
  // Document Processing Steps
  @DBOS.step()
  static async validateDocument(document: ComplianceDocument): Promise<boolean> {
    DBOS.logger.info(`Validating document ${document.id}`);
    
    // Simulate document validation
    await DBOS.sleep(1000);
    
    // Check document format and completeness
    if (!document.content || document.content.length < 100) {
      DBOS.logger.warn(`Document ${document.id} failed validation - insufficient content`);
      return false;
    }
    
    DBOS.logger.info(`Document ${document.id} validation completed`);
    return true;
  }

  @DBOS.step()
  static async scanForViolations(document: ComplianceDocument): Promise<ComplianceViolation[]> {
    DBOS.logger.info(`Scanning document ${document.id} for compliance violations`);

    const violations: ComplianceViolation[] = [];

    // Simulate AI-powered compliance scanning
    await DBOS.sleep(2000);

    // Get active compliance rules from database
    const rules = await ComplianceDatabase.getActiveComplianceRules();

    for (const rule of rules) {
      const regex = new RegExp(rule.pattern, 'gi');
      const matches = document.content.match(regex);

      if (matches) {
        // Check if this represents a violation based on context
        const isViolation = await ComplianceSystem.analyzeViolationContext(
          document.content,
          rule,
          matches
        );

        if (isViolation) {
          violations.push({
            documentId: document.id,
            ruleId: rule.id,
            violationType: rule.ruleType,
            description: `Potential ${rule.standard} violation: ${rule.description}`,
            severity: rule.severity,
            recommendedAction: ComplianceSystem.getRecommendedAction(rule),
            detectedAt: new Date()
          });
        }
      }
    }

    DBOS.logger.info(`Found ${violations.length} violations in document ${document.id}`);

    // Save violations to database
    if (violations.length > 0) {
      await ComplianceDatabase.saveViolations(violations);
    }

    return violations;
  }

  @DBOS.step()
  static async analyzeViolationContext(
    content: string, 
    rule: ComplianceRule, 
    matches: string[]
  ): Promise<boolean> {
    // Simulate AI context analysis
    await DBOS.sleep(500);
    
    // Simple heuristic - in production, this would use ML models
    const contextWindow = 200;
    let hasViolation = false;
    
    for (const match of matches) {
      const matchIndex = content.indexOf(match);
      const context = content.substring(
        Math.max(0, matchIndex - contextWindow),
        Math.min(content.length, matchIndex + contextWindow + match.length)
      );
      
      // Check for protective measures or compliance statements
      const protectivePatterns = [
        'encrypted', 'protected', 'secure', 'compliant', 
        'privacy policy', 'data protection', 'authorized access'
      ];
      
      const hasProtection = protectivePatterns.some(pattern => 
        context.toLowerCase().includes(pattern)
      );
      
      if (!hasProtection && rule.severity === 'critical') {
        hasViolation = true;
        break;
      }
    }
    
    return hasViolation;
  }

  @DBOS.step()
  static async notifyComplianceTeam(violations: ComplianceViolation[]): Promise<void> {
    DBOS.logger.info(`Notifying compliance team of ${violations.length} violations`);
    
    // Simulate notification to compliance team
    await DBOS.sleep(500);
    
    const criticalViolations = violations.filter(v => v.severity === 'critical');
    const highViolations = violations.filter(v => v.severity === 'high');
    
    if (criticalViolations.length > 0) {
      DBOS.logger.warn(`CRITICAL: ${criticalViolations.length} critical violations detected`);
      // In production: send urgent notifications, create incidents
    }
    
    if (highViolations.length > 0) {
      DBOS.logger.warn(`HIGH: ${highViolations.length} high-severity violations detected`);
      // In production: send priority notifications
    }
    
    DBOS.logger.info('Compliance team notifications sent');
  }

  // KYC Processing Steps
  @DBOS.step()
  static async verifyIdentity(profile: KYCProfile): Promise<{ verified: boolean; confidence: number }> {
    DBOS.logger.info(`Verifying identity for customer ${profile.customerId}`);
    
    // Simulate identity verification via third-party services
    await DBOS.sleep(3000);
    
    // Mock verification logic
    const hasValidSSN = profile.personalInfo.ssn.length === 11; // XXX-XX-XXXX format
    const hasValidDOB = new Date(profile.personalInfo.dateOfBirth) < new Date();
    const hasValidAddress = profile.personalInfo.address.length > 10;
    
    const confidence = (hasValidSSN ? 0.4 : 0) + 
                      (hasValidDOB ? 0.3 : 0) + 
                      (hasValidAddress ? 0.3 : 0);
    
    const verified = confidence >= 0.8;
    
    DBOS.logger.info(`Identity verification completed: ${verified ? 'PASSED' : 'FAILED'} (${confidence})`);
    return { verified, confidence };
  }

  @DBOS.step()
  static async performRiskAssessment(profile: KYCProfile): Promise<number> {
    DBOS.logger.info(`Performing risk assessment for customer ${profile.customerId}`);
    
    // Simulate risk assessment
    await DBOS.sleep(2000);
    
    let riskScore = 0;
    
    // Age-based risk (younger = higher risk)
    const age = new Date().getFullYear() - new Date(profile.personalInfo.dateOfBirth).getFullYear();
    if (age < 25) riskScore += 20;
    else if (age < 35) riskScore += 10;
    
    // Address-based risk (simplified)
    const highRiskZipPrefixes = ['900', '800', '700']; // Mock high-risk areas
    const zipCode = profile.personalInfo.address.match(/\d{5}/)?.[0];
    if (zipCode && highRiskZipPrefixes.some(prefix => zipCode.startsWith(prefix))) {
      riskScore += 30;
    }
    
    // Random factor for demonstration
    riskScore += Math.floor(Math.random() * 20);
    
    DBOS.logger.info(`Risk assessment completed: score ${riskScore}`);
    return Math.min(riskScore, 100);
  }

  @DBOS.step()
  static async checkSanctionsList(profile: KYCProfile): Promise<{ isListed: boolean; details?: string }> {
    DBOS.logger.info(`Checking sanctions list for customer ${profile.customerId}`);
    
    // Simulate sanctions list check
    await DBOS.sleep(1500);
    
    // Mock sanctions check - in production, this would query OFAC, UN, etc.
    const sanctionedNames = ['John Doe', 'Jane Smith']; // Mock list
    const isListed = sanctionedNames.includes(profile.personalInfo.name);
    
    const result = {
      isListed,
      details: isListed ? 'Found match in OFAC sanctions list' : undefined
    };
    
    DBOS.logger.info(`Sanctions check completed: ${isListed ? 'MATCH FOUND' : 'CLEAR'}`);
    return result;
  }

  // Regulatory Monitoring Steps
  @DBOS.step()
  static async fetchRegulatoryUpdates(): Promise<RegulatoryUpdate[]> {
    DBOS.logger.info('Fetching latest regulatory updates');

    // Simulate fetching from regulatory websites/APIs
    await DBOS.sleep(2000);

    // Get regulatory updates from database
    const updates = await ComplianceDatabase.getRegulatoryUpdates();

    DBOS.logger.info(`Fetched ${updates.length} regulatory updates from database`);
    return updates;
  }

  @DBOS.step()
  static async analyzeRegulatoryImpact(updates: RegulatoryUpdate[]): Promise<string[]> {
    DBOS.logger.info('Analyzing regulatory impact');
    
    await DBOS.sleep(1000);
    
    const recommendations: string[] = [];
    
    for (const update of updates) {
      if (update.actionRequired) {
        switch (update.impact) {
          case 'high':
            recommendations.push(`URGENT: Review and update policies for ${update.title}`);
            recommendations.push(`URGENT: Train compliance team on ${update.standard} changes`);
            break;
          case 'medium':
            recommendations.push(`PRIORITY: Update procedures for ${update.title}`);
            break;
          case 'low':
            recommendations.push(`MONITOR: Track implementation of ${update.title}`);
            break;
        }
      }
    }
    
    DBOS.logger.info(`Generated ${recommendations.length} recommendations`);
    return recommendations;
  }

  // Report Generation Steps
  @DBOS.step()
  static async generateComplianceMetrics(): Promise<{
    totalDocuments: number;
    compliantDocuments: number;
    violationsCount: number;
    complianceRate: number;
  }> {
    DBOS.logger.info('Generating compliance metrics');

    await DBOS.sleep(1000);

    // Get real metrics from database
    const metrics = await ComplianceDatabase.getComplianceMetrics();

    DBOS.logger.info(`Generated metrics: ${metrics.totalDocuments} total documents, ${metrics.complianceRate}% compliance rate`);

    return metrics;
  }

  @DBOS.step()
  static async formatComplianceReport(
    metrics: {
      totalDocuments: number;
      compliantDocuments: number;
      violationsCount: number;
      complianceRate: number;
    },
    violations: ComplianceViolation[],
    recommendations: string[]
  ): Promise<ComplianceReport> {
    DBOS.logger.info('Formatting compliance report');

    await DBOS.sleep(500);

    const report: ComplianceReport = {
      id: `RPT-${Date.now()}`,
      reportType: 'monthly',
      generatedAt: new Date(),
      compliance_rate: metrics.complianceRate,
      violations: violations.slice(0, 10), // Top 10 violations
      recommendations
    };

    // Save report to database
    await ComplianceDatabase.saveComplianceReport(report);

    DBOS.logger.info(`Compliance report ${report.id} formatted and saved to database`);
    return report;
  }

  // Workflow Orchestration
  @DBOS.workflow()
  static async processComplianceDocument(document: ComplianceDocument): Promise<{
    status: string;
    violations: ComplianceViolation[];
  }> {
    DBOS.logger.info(`Starting compliance processing for document ${document.id}`);

    // Emit processing status
    await DBOS.setEvent('processing_status', 'started');

    // Save document to database
    await ComplianceDatabase.saveDocument(document);

    // Step 1: Validate document
    const isValid = await ComplianceSystem.validateDocument(document);
    if (!isValid) {
      await DBOS.setEvent('processing_status', 'failed_validation');
      // Update document status in database
      document.status = 'requires_review';
      await ComplianceDatabase.saveDocument(document);
      return { status: 'invalid', violations: [] };
    }

    await DBOS.setEvent('processing_status', 'validation_passed');

    // Step 2: Scan for violations
    const violations = await ComplianceSystem.scanForViolations(document);

    await DBOS.setEvent('violations_found', violations.length);

    // Step 3: Notify compliance team if violations found
    if (violations.length > 0) {
      await ComplianceSystem.notifyComplianceTeam(violations);
      await DBOS.setEvent('processing_status', 'violations_reported');
    }

    await DBOS.setEvent('processing_status', 'completed');

    const status = violations.length > 0 ? 'non_compliant' : 'compliant';

    // Update document status in database
    document.status = status as 'compliant' | 'non_compliant';
    await ComplianceDatabase.saveDocument(document);

    DBOS.logger.info(`Compliance processing completed for document ${document.id}: ${status}`);

    return { status, violations };
  }

  @DBOS.workflow()
  static async processKYCCustomer(profile: KYCProfile): Promise<{
    status: 'approved' | 'rejected' | 'under_review';
    riskScore: number;
    reasons: string[];
  }> {
    DBOS.logger.info(`Starting KYC processing for customer ${profile.customerId}`);

    await DBOS.setEvent('kyc_status', 'identity_verification');

    // Save initial KYC profile to database
    profile.status = 'pending';
    await ComplianceDatabase.saveKYCProfile(profile);

    // Step 1: Identity verification
    const identityResult = await ComplianceSystem.verifyIdentity(profile);

    if (!identityResult.verified) {
      await DBOS.setEvent('kyc_status', 'identity_failed');
      // Update profile status in database
      profile.status = 'rejected';
      profile.riskScore = 100;
      await ComplianceDatabase.saveKYCProfile(profile);
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: ['Identity verification failed']
      };
    }

    await DBOS.setEvent('kyc_status', 'risk_assessment');

    // Step 2: Risk assessment
    const riskScore = await ComplianceSystem.performRiskAssessment(profile);
    profile.riskScore = riskScore;

    await DBOS.setEvent('kyc_status', 'sanctions_check');

    // Step 3: Sanctions list check
    const sanctionsResult = await ComplianceSystem.checkSanctionsList(profile);

    if (sanctionsResult.isListed) {
      await DBOS.setEvent('kyc_status', 'sanctions_match');
      // Update profile status in database
      profile.status = 'rejected';
      profile.riskScore = 100;
      await ComplianceDatabase.saveKYCProfile(profile);
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: [`Sanctions list match: ${sanctionsResult.details}`]
      };
    }

    // Determine final status
    let status: 'approved' | 'rejected' | 'under_review';
    const reasons: string[] = [];

    if (riskScore >= 70) {
      status = 'under_review';
      reasons.push('High risk score requires manual review');
    } else if (riskScore >= 50) {
      status = 'under_review';
      reasons.push('Medium risk score requires additional verification');
    } else {
      status = 'approved';
      reasons.push('Low risk profile - automatically approved');
    }

    // Update final profile status in database
    profile.status = status;
    profile.lastUpdated = new Date();
    await ComplianceDatabase.saveKYCProfile(profile);

    await DBOS.setEvent('kyc_status', 'completed');
    await DBOS.setEvent('final_status', status);

    DBOS.logger.info(`KYC processing completed for customer ${profile.customerId}: ${status}`);

    return { status, riskScore, reasons };
  }

  @DBOS.workflow()
  static async generateComplianceReport(reportType: 'monthly' | 'quarterly' | 'annual'): Promise<ComplianceReport> {
    DBOS.logger.info(`Generating ${reportType} compliance report`);
    
    await DBOS.setEvent('report_status', 'metrics_generation');
    
    // Step 1: Generate metrics
    const metrics = await ComplianceSystem.generateComplianceMetrics();
    
    await DBOS.setEvent('report_status', 'regulatory_updates');
    
    // Step 2: Fetch regulatory updates
    const regulatoryUpdates = await ComplianceSystem.fetchRegulatoryUpdates();
    
    await DBOS.setEvent('report_status', 'impact_analysis');
    
    // Step 3: Analyze impact
    const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(regulatoryUpdates);
    
    await DBOS.setEvent('report_status', 'formatting');
    
    // Step 4: Format report
    const report = await ComplianceSystem.formatComplianceReport(
      metrics, 
      [], // Mock violations for report
      recommendations
    );
    
    await DBOS.setEvent('report_status', 'completed');
    await DBOS.setEvent('report_id', report.id);
    
    DBOS.logger.info(`Compliance report ${report.id} generated successfully`);
    
    return report;
  }

  @DBOS.scheduled({ crontab: "0 9 * * 1" }) // Every Monday at 9 AM
  @DBOS.workflow()
  static async weeklyRegulatoryMonitoring(scheduledTime: Date, _startTime: Date): Promise<void> {
    DBOS.logger.info(`Starting weekly regulatory monitoring at ${scheduledTime}`);
    
    // Fetch and analyze regulatory updates
    const updates = await ComplianceSystem.fetchRegulatoryUpdates();
    const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(updates);
    
    // Emit findings for monitoring
    await DBOS.setEvent('weekly_updates_count', updates.length);
    await DBOS.setEvent('weekly_recommendations', recommendations);
    
    DBOS.logger.info(`Weekly regulatory monitoring completed - ${updates.length} updates processed`);
  }

  // Utility methods
  static getRecommendedAction(rule: ComplianceRule): string {
    switch (rule.severity) {
      case 'critical':
        return 'Immediate remediation required - escalate to legal team';
      case 'high':
        return 'Priority remediation - update within 24 hours';
      case 'medium':
        return 'Schedule remediation within 1 week';
      case 'low':
        return 'Monitor and address in next review cycle';
      default:
        return 'Review and assess appropriate action';
    }
  }
}

// API Endpoints
app.post('/api/compliance/document', async (req: Request, res: Response): Promise<void> => {
  try {
    const document: ComplianceDocument = req.body;

    // Start compliance processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: complianceQueue.name }
    ).processComplianceDocument(document);

    // Return workflow ID for tracking
    res.json({
      workflowId: handle.workflowID,
      status: 'processing_started',
      message: 'Document compliance check initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing document: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/kyc/customer', async (req: Request, res: Response): Promise<void> => {
  try {
    const profile: KYCProfile = req.body;

    // Start KYC processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: kycQueue.name }
    ).processKYCCustomer(profile);

    res.json({
      workflowId: handle.workflowID,
      status: 'kyc_processing_started',
      message: 'KYC verification initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing KYC: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/reports/generate', async (req: Request, res: Response): Promise<void> => {
  try {
    const { reportType } = req.body;

    if (!['monthly', 'quarterly', 'annual'].includes(reportType)) {
      res.status(400).json({ error: 'Invalid report type' });
      return;
    }

    // Start report generation workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: reportingQueue.name }
    ).generateComplianceReport(reportType);

    res.json({
      workflowId: handle.workflowID,
      status: 'report_generation_started',
      message: `${reportType} compliance report generation initiated`
    });
  } catch (error) {
    DBOS.logger.error(`Error generating report: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/workflow/:workflowId/status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get status
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const status = await handle.getStatus();

    // Check if status is null
    if (!status) {
      res.status(404).json({ error: 'Workflow status not available' });
      return;
    }

    // Get workflow events for detailed progress
    const events: Record<string, unknown> = {};
    try {
      events['processing_status'] = await DBOS.getEvent(workflowId, 'processing_status', 1);
      events['violations_found'] = await DBOS.getEvent(workflowId, 'violations_found', 1);
      events['kyc_status'] = await DBOS.getEvent(workflowId, 'kyc_status', 1);
      events['report_status'] = await DBOS.getEvent(workflowId, 'report_status', 1);
    } catch (eventError) {
      // Events might not exist yet
      DBOS.logger.info(`No events found for workflow ${workflowId}`);
    }

    res.json({
      workflowId,
      status: status.status,
      workflowName: status.workflowName,
      events
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow status: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found' });
  }
});

app.get('/api/workflow/:workflowId/result', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get result
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const result = await handle.getResult();

    res.json({
      workflowId,
      result
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow result: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found or not completed' });
  }
});

// Dashboard API Endpoints
app.get('/api/dashboard/metrics', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📊 Dashboard metrics requested');

    // Get real metrics from database
    const metrics = await ComplianceDatabase.getDashboardMetrics();

    res.json(metrics);
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    // Fallback to mock data if database is not available
    const now = new Date();
    const hourOfDay = now.getHours();
    const dayOfWeek = now.getDay();

    const baseCompliance = 98.2;
    const timeVariation = Math.sin(hourOfDay / 24 * Math.PI * 2) * 0.3;
    const weekVariation = dayOfWeek === 0 || dayOfWeek === 6 ? -0.2 : 0;

    const fallbackMetrics = {
      complianceRate: Math.round((baseCompliance + timeVariation + weekVariation) * 10) / 10,
      activeViolations: 3 + (hourOfDay > 16 ? 1 : 0),
      pendingKYC: 24 + Math.floor(Math.random() * 6) - 3,
      completedReports: 156 + Math.floor((now.getDate() - 1) * 2.3),
      regulatoryUpdates: 7 + (dayOfWeek === 1 ? 2 : 0)
    };
    res.json(fallbackMetrics);
  }
});

app.get('/api/dashboard/compliance-standards', (_req: Request, res: Response): void => {
  console.log('📋 Compliance standards requested');
  const standards = [
    {
      name: 'SEC Regulations',
      compliance: 98,
      violations: 1,
      lastCheck: '2024-06-09',
      status: 'issues' as const
    },
    {
      name: 'GLBA Requirements',
      compliance: 95,
      violations: 2,
      lastCheck: '2024-06-09',
      status: 'issues' as const
    },
    {
      name: 'SOX Compliance',
      compliance: 99,
      violations: 0,
      lastCheck: '2024-06-08',
      status: 'compliant' as const
    },
    {
      name: 'FINRA Rules',
      compliance: 97,
      violations: 0,
      lastCheck: '2024-06-08',
      status: 'compliant' as const
    }
  ];
  res.json(standards);
});

app.get('/api/dashboard/recent-violations', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('🚨 Recent violations requested');

    // Get real violations from database
    const violations = await ComplianceDatabase.getRecentViolations();

    res.json(violations);
  } catch (error) {
    console.error('Error fetching recent violations:', error);
    // Fallback to mock data if database is not available
    const fallbackViolations = [
      {
        id: 1,
        document: 'Q3_Financial_Report.pdf',
        violation: 'Missing SOX disclosure statement',
        severity: 'Critical' as const,
        date: '2024-06-08',
        status: 'Under Review'
      },
      {
        id: 2,
        document: 'Customer_Agreement_v2.pdf',
        violation: 'GLBA privacy notice incomplete',
        severity: 'High' as const,
        date: '2024-06-07',
        status: 'Remediation Required'
      },
      {
        id: 3,
        document: 'Risk_Assessment_May.pdf',
        violation: 'SEC reporting format non-compliance',
        severity: 'Medium' as const,
        date: '2024-06-06',
        status: 'In Progress'
      }
    ];
    res.json(fallbackViolations);
  }
});

app.get('/api/dashboard/ai-insights', (_req: Request, res: Response): void => {
  const insights = [
    {
      type: 'pattern' as const,
      title: 'Pattern Detection',
      description: 'SOX disclosure statements missing in 60% of Q3 documents',
      color: 'blue' as const
    },
    {
      type: 'improvement' as const,
      title: 'Improvement Trend',
      description: 'GLBA compliance improved 15% this quarter',
      color: 'green' as const
    },
    {
      type: 'risk' as const,
      title: 'Risk Alert',
      description: 'New SEC rule may impact 23 existing documents',
      color: 'orange' as const
    }
  ];
  res.json(insights);
});

app.get('/api/dashboard/workflow-performance', (_req: Request, res: Response): void => {
  const performance = {
    documentProcessing: '2.3s avg',
    kycCompletionRate: '94%',
    zeroDowntime: '99.9%',
    costSavings: '$2.4M'
  };
  res.json(performance);
});

// Document API Endpoints
app.get('/api/documents/recent', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📄 Recent documents requested');

    // Get real documents from database
    const recentDocuments = await ComplianceDatabase.getRecentDocuments();

    res.json(recentDocuments);
  } catch (error) {
    console.error('Error fetching recent documents:', error);
    // Fallback to mock data if database is not available
    const fallbackDocuments = [
      {
        id: 1,
        name: 'Q3_Financial_Report.pdf',
        size: '2.4 MB',
        status: 'Violation Detected',
        violations: 2,
        uploadDate: '2024-06-09 14:30',
        complianceChecks: ['SEC', 'SOX', 'GLBA']
      },
      {
        id: 2,
        name: 'Risk_Assessment_June.docx',
        size: '1.8 MB',
        status: 'Compliant',
        violations: 0,
        uploadDate: '2024-06-09 11:15',
        complianceChecks: ['SEC', 'FINRA']
      },
      {
        id: 3,
        name: 'Customer_Agreement_v3.pdf',
        size: '956 KB',
        status: 'Processing',
        violations: null,
        uploadDate: '2024-06-09 10:45',
        complianceChecks: ['GLBA', 'CCPA']
      },
      {
        id: 4,
        name: 'Policy_Update_May.pdf',
        size: '1.2 MB',
        status: 'Compliant',
        violations: 0,
        uploadDate: '2024-06-08 16:20',
        complianceChecks: ['SOX', 'GDPR']
      },
      {
        id: 5,
        name: 'Audit_Report_Q2.pdf',
        size: '3.1 MB',
        status: 'Violation Detected',
        violations: 1,
        uploadDate: '2024-06-08 09:30',
        complianceChecks: ['SEC', 'SOX', 'FINRA']
      }
    ];
    res.json(fallbackDocuments);
  }
});

app.get('/api/documents/stats', (_req: Request, res: Response): void => {
  console.log('📊 Document stats requested');

  // Add realistic variations based on current time
  const now = new Date();
  const dayOfMonth = now.getDate();
  const hourOfDay = now.getHours();

  // Simulate document processing growth throughout the month
  const baseDocuments = 1247;
  const dailyGrowth = dayOfMonth * 15; // ~15 docs per day
  const hourlyVariation = Math.floor(hourOfDay / 8) * 3; // More during business hours

  const stats = {
    documentsScanned: baseDocuments + dailyGrowth + hourlyVariation,
    violationsDetected: 23 + Math.floor(dailyGrowth * 0.018), // ~1.8% violation rate
    avgProcessingTime: '2.3s',
    violationRate: Math.round((23 + Math.floor(dailyGrowth * 0.018)) / (baseDocuments + dailyGrowth) * 100 * 10) / 10
  };
  res.json(stats);
});

// Regulatory API Endpoints
app.get('/api/regulatory/updates', (_req: Request, res: Response): void => {
  console.log('📋 Regulatory updates requested');
  const regulatoryUpdates = [
    {
      id: 1,
      source: 'SEC',
      title: 'Updated Cybersecurity Disclosure Requirements',
      description: 'New rules require enhanced cybersecurity incident reporting within 4 business days',
      publishDate: '2024-06-08',
      effectiveDate: '2024-09-01',
      impact: 'High',
      affectedDocuments: 23,
      status: 'Action Required',
      url: '#'
    },
    {
      id: 2,
      source: 'GLBA',
      title: 'Enhanced Privacy Notice Requirements',
      description: 'Updated consumer privacy notice standards for financial institutions',
      publishDate: '2024-06-05',
      effectiveDate: '2024-08-15',
      impact: 'Medium',
      affectedDocuments: 15,
      status: 'Under Review',
      url: '#'
    },
    {
      id: 3,
      source: 'FINRA',
      title: 'Digital Asset Trading Guidelines',
      description: 'New guidance on cryptocurrency and digital asset trading compliance',
      publishDate: '2024-06-03',
      effectiveDate: '2024-07-01',
      impact: 'Low',
      affectedDocuments: 8,
      status: 'Monitoring',
      url: '#'
    },
    {
      id: 4,
      source: 'SOX',
      title: 'Internal Control Assessment Updates',
      description: 'Clarifications on management assessment of internal controls',
      publishDate: '2024-06-01',
      effectiveDate: '2024-12-31',
      impact: 'Medium',
      affectedDocuments: 31,
      status: 'Implemented',
      url: '#'
    },
    {
      id: 5,
      source: 'CCPA',
      title: 'Consumer Rights Amendment',
      description: 'New consumer data deletion and portability requirements',
      publishDate: '2024-05-28',
      effectiveDate: '2024-08-01',
      impact: 'High',
      affectedDocuments: 18,
      status: 'Under Review',
      url: '#'
    },
    {
      id: 6,
      source: 'GDPR',
      title: 'AI Processing Guidelines',
      description: 'Updated guidance on AI and automated decision-making compliance',
      publishDate: '2024-05-25',
      effectiveDate: '2024-07-15',
      impact: 'Medium',
      affectedDocuments: 12,
      status: 'Monitoring',
      url: '#'
    }
  ];
  res.json(regulatoryUpdates);
});

app.get('/api/regulatory/stats', (_req: Request, res: Response): void => {
  console.log('📈 Regulatory stats requested');
  const stats = {
    totalUpdates: 47,
    pendingReview: 12,
    actionRequired: 8,
    avgResponseTime: '2.1 days'
  };
  res.json(stats);
});

app.get('/api/regulatory/sources', (_req: Request, res: Response): void => {
  console.log('🔍 Regulatory sources requested');
  const monitoredSources = [
    { name: 'SEC', status: 'Active', lastCheck: '2024-06-09 08:00', updates: 15 },
    { name: 'GLBA', status: 'Active', lastCheck: '2024-06-09 08:00', updates: 8 },
    { name: 'SOX', status: 'Active', lastCheck: '2024-06-09 08:00', updates: 12 },
    { name: 'FINRA', status: 'Active', lastCheck: '2024-06-09 08:00', updates: 7 },
    { name: 'CCPA', status: 'Active', lastCheck: '2024-06-09 08:00', updates: 5 }
  ];
  res.json(monitoredSources);
});

// KYC API Endpoints
app.get('/api/kyc/queue', (_req: Request, res: Response): void => {
  console.log('👥 KYC queue requested');
  const kycQueue = [
    {
      id: 'KYC-2024-001',
      customerName: 'John Anderson',
      submissionDate: '2024-06-09 10:30',
      status: 'Identity Verification',
      riskScore: 'Low',
      timeRemaining: '2 hours',
      completedSteps: 2,
      totalSteps: 4,
      flags: []
    },
    {
      id: 'KYC-2024-002',
      customerName: 'Sarah Michelle Corp',
      submissionDate: '2024-06-09 09:15',
      status: 'Sanctions Screening',
      riskScore: 'Medium',
      timeRemaining: '4 hours',
      completedSteps: 3,
      totalSteps: 4,
      flags: ['Corporate Entity']
    },
    {
      id: 'KYC-2024-003',
      customerName: 'Robert Chen',
      submissionDate: '2024-06-08 16:45',
      status: 'Manual Review Required',
      riskScore: 'High',
      timeRemaining: 'Overdue',
      completedSteps: 3,
      totalSteps: 4,
      flags: ['High Risk Country', 'PEP Check']
    },
    {
      id: 'KYC-2024-004',
      customerName: 'Emma Technologies Ltd',
      submissionDate: '2024-06-08 14:20',
      status: 'Completed',
      riskScore: 'Low',
      timeRemaining: 'Completed',
      completedSteps: 4,
      totalSteps: 4,
      flags: []
    },
    {
      id: 'KYC-2024-005',
      customerName: 'Michael Rodriguez',
      submissionDate: '2024-06-08 11:30',
      status: 'Risk Assessment',
      riskScore: 'Medium',
      timeRemaining: '6 hours',
      completedSteps: 1,
      totalSteps: 4,
      flags: ['First Time Customer']
    },
    {
      id: 'KYC-2024-006',
      customerName: 'Global Finance Inc',
      submissionDate: '2024-06-08 09:15',
      status: 'Document Review',
      riskScore: 'Medium',
      timeRemaining: '3 hours',
      completedSteps: 2,
      totalSteps: 4,
      flags: ['Corporate Entity', 'Multiple Jurisdictions']
    },
    {
      id: 'KYC-2024-007',
      customerName: 'Alice Johnson',
      submissionDate: '2024-06-07 15:45',
      status: 'Completed',
      riskScore: 'Low',
      timeRemaining: 'Completed',
      completedSteps: 4,
      totalSteps: 4,
      flags: []
    }
  ];
  res.json(kycQueue);
});

app.get('/api/kyc/stats', (_req: Request, res: Response): void => {
  console.log('📊 KYC stats requested');
  const stats = {
    totalProcessed: 1456,
    averageTime: '2.3 days',
    automationRate: 94,
    pendingReview: 23
  };
  res.json(stats);
});

// Reports API Endpoints
app.get('/api/reports/recent', (_req: Request, res: Response): void => {
  console.log('📄 Recent reports requested');
  const recentReports = [
    {
      id: 'RPT-2024-045',
      name: 'Q2 2024 Compliance Summary',
      type: 'Compliance Summary',
      generatedDate: '2024-06-09 09:30',
      status: 'Completed',
      size: '2.4 MB',
      pages: 15,
      recipients: ['Board', 'Executive Team']
    },
    {
      id: 'RPT-2024-044',
      name: 'June KYC Status Report',
      type: 'KYC Status',
      generatedDate: '2024-06-08 16:45',
      status: 'Completed',
      size: '1.8 MB',
      pages: 8,
      recipients: ['Operations', 'Compliance']
    },
    {
      id: 'RPT-2024-043',
      name: 'May Violations Analysis',
      type: 'Violations Analysis',
      generatedDate: '2024-06-07 14:20',
      status: 'Generating',
      size: 'Pending',
      pages: 'Pending',
      recipients: ['Compliance Team']
    },
    {
      id: 'RPT-2024-042',
      name: 'Q2 Regulatory Impact Assessment',
      type: 'Regulatory Impact',
      generatedDate: '2024-06-06 10:15',
      status: 'Completed',
      size: '3.2 MB',
      pages: 22,
      recipients: ['Executives', 'Legal']
    },
    {
      id: 'RPT-2024-041',
      name: 'May Audit Trail Report',
      type: 'Audit Trail',
      generatedDate: '2024-06-05 13:45',
      status: 'Completed',
      size: '5.1 MB',
      pages: 35,
      recipients: ['Auditors', 'Regulators']
    }
  ];
  res.json(recentReports);
});

app.get('/api/reports/stats', (_req: Request, res: Response): void => {
  console.log('📈 Report stats requested');
  const stats = {
    totalGenerated: 245,
    averageTime: '45 seconds',
    automationRate: 100,
    pagesSaved: '12,500+'
  };
  res.json(stats);
});

// Workflows API Endpoints
app.get('/api/workflows/active', (_req: Request, res: Response): void => {
  console.log('⚡ Active workflows requested');
  const activeWorkflows = [
    {
      id: 'WF-COMP-2024-156',
      type: 'Compliance Check',
      document: 'Q3_Financial_Report.pdf',
      status: 'Running',
      progress: 75,
      currentStep: 'AI Violation Detection',
      totalSteps: 4,
      startTime: '2024-06-09 14:30:00',
      estimatedCompletion: '2024-06-09 14:35:00',
      executor: 'DBOS-Worker-01'
    },
    {
      id: 'WF-KYC-2024-089',
      type: 'KYC Process',
      document: 'Customer: John Anderson',
      status: 'Running',
      progress: 50,
      currentStep: 'Sanctions Screening',
      totalSteps: 4,
      startTime: '2024-06-09 14:15:00',
      estimatedCompletion: '2024-06-09 14:45:00',
      executor: 'DBOS-Worker-02'
    },
    {
      id: 'WF-REP-2024-034',
      type: 'Report Generation',
      document: 'Monthly Compliance Summary',
      status: 'Paused',
      progress: 25,
      currentStep: 'Data Collection',
      totalSteps: 5,
      startTime: '2024-06-09 13:45:00',
      estimatedCompletion: 'Paused',
      executor: 'DBOS-Worker-03'
    },
    {
      id: 'WF-MON-2024-012',
      type: 'Regulatory Monitoring',
      document: 'SEC Rule Updates Check',
      status: 'Completed',
      progress: 100,
      currentStep: 'Completed',
      totalSteps: 3,
      startTime: '2024-06-09 14:00:00',
      estimatedCompletion: '2024-06-09 14:05:00',
      executor: 'DBOS-Worker-04'
    },
    {
      id: 'WF-COMP-2024-157',
      type: 'Compliance Check',
      document: 'Risk_Assessment_June.docx',
      status: 'Running',
      progress: 90,
      currentStep: 'Final Validation',
      totalSteps: 4,
      startTime: '2024-06-09 14:20:00',
      estimatedCompletion: '2024-06-09 14:25:00',
      executor: 'DBOS-Worker-05'
    }
  ];
  res.json(activeWorkflows);
});

app.get('/api/workflows/stats', (_req: Request, res: Response): void => {
  console.log('📊 Workflow stats requested');
  const stats = {
    totalExecuted: 2456,
    currentlyRunning: 15,
    avgExecutionTime: '3.2 min',
    successRate: 99.7
  };
  res.json(stats);
});

app.get('/api/workflows/metrics', (_req: Request, res: Response): void => {
  console.log('📈 Workflow metrics requested');
  const dbosMetrics = [
    { metric: 'Workflow Durability', value: '100%', description: 'Zero workflow data loss' },
    { metric: 'Fault Recovery', value: '< 1s', description: 'Average recovery time' },
    { metric: 'Concurrency Control', value: '50+', description: 'Parallel workflows' },
    { metric: 'Resource Utilization', value: '85%', description: 'Optimal efficiency' }
  ];
  res.json(dbosMetrics);
});

// Additional API endpoints for better frontend integration
app.get('/api/dashboard/violation-details', (_req: Request, res: Response): void => {
  console.log('🚨 Violation details requested');
  const violationDetails = {
    critical: 2,
    high: 1,
    medium: 0,
    low: 0,
    total: 3,
    breakdown: [
      { type: 'Critical', count: 2, description: 'SOX disclosure missing' },
      { type: 'High', count: 1, description: 'GLBA privacy notice incomplete' }
    ]
  };
  res.json(violationDetails);
});

app.get('/api/dashboard/processing-metrics', (_req: Request, res: Response): void => {
  console.log('⚡ Processing metrics requested');
  const processingMetrics = {
    avgKycTime: '2.3 days',
    avgDocumentTime: '2.3s',
    processingImprovement: '90%',
    automationRate: '94%'
  };
  res.json(processingMetrics);
});

// Enhanced mock data endpoints
app.get('/api/compliance/violations/summary', (_req: Request, res: Response): void => {
  console.log('📊 Violation summary requested');
  const now = new Date();
  const dayOfWeek = now.getDay();

  // Simulate more violations found during weekdays
  const weekdayMultiplier = dayOfWeek === 0 || dayOfWeek === 6 ? 0.7 : 1.0;

  const summary = {
    total: Math.floor(23 * weekdayMultiplier),
    bySeverity: {
      critical: Math.floor(2 * weekdayMultiplier),
      high: Math.floor(5 * weekdayMultiplier),
      medium: Math.floor(8 * weekdayMultiplier),
      low: Math.floor(8 * weekdayMultiplier)
    },
    byStandard: {
      SEC: Math.floor(8 * weekdayMultiplier),
      GLBA: Math.floor(6 * weekdayMultiplier),
      SOX: Math.floor(4 * weekdayMultiplier),
      FINRA: Math.floor(3 * weekdayMultiplier),
      CCPA: Math.floor(2 * weekdayMultiplier)
    },
    trends: {
      thisWeek: Math.floor(5 * weekdayMultiplier),
      lastWeek: 7,
      change: Math.floor((5 * weekdayMultiplier - 7) / 7 * 100)
    }
  };
  res.json(summary);
});

app.get('/api/system/health', (_req: Request, res: Response): void => {
  console.log('🏥 System health requested');
  const health = {
    status: 'healthy',
    uptime: '99.9%',
    lastIncident: '2024-05-15',
    services: {
      database: 'healthy',
      workflows: 'healthy',
      api: 'healthy',
      monitoring: 'healthy'
    },
    performance: {
      responseTime: '< 100ms',
      throughput: '1,250 req/min',
      errorRate: '0.01%'
    }
  };
  res.json(health);
});

// Health check endpoint
app.get('/health', (_req: Request, res: Response): void => {
  res.json({
    status: 'healthy',
    service: 'regulatory-compliance-system',
    timestamp: new Date().toISOString()
  });
});

// Serve static files from dist directory
app.use(express.static(path.resolve('dist')));

// Handle static assets with specific extensions
app.get(/\.(js|css|svg|txt|ico|png|jpg|jpeg|gif|woff|woff2|ttf|eot)$/, (req: Request, res: Response): void => {
  const requestPath = req.path;
  console.log(`Static file request: ${requestPath}`);
  if(requestPath === '/') {
    console.log('Serving index.html');
    res.sendFile(path.resolve('dist', 'index.html'));
    return;
  }

  // Try to serve from dist/assets first, then from dist root
  const assetsPath = path.resolve('dist', 'assets', path.basename(requestPath));
  const rootPath = path.resolve('dist', requestPath.substring(1));

  // Check if file exists in assets directory first
  if (fs.existsSync(assetsPath)) {
    return res.sendFile(assetsPath);
  } else if (fs.existsSync(rootPath)) {
    return res.sendFile(rootPath);
  }

  // If file not found, return 404
  res.status(404).send('File not found');
});

// SPA fallback - serve index.html for all other routes (only in production)
/*if (process.env.NODE_ENV !== 'test') {
  app.get('*', (_req: Request, res: Response): void => {
    res.sendFile(path.resolve('dist', 'index.html'));
  });
}*/

// Main function
async function main() {
  // Configuration is loaded from dbos-config.yaml
  await DBOS.launch({ expressApp: app });

  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    console.log(`🏛️  Regulatory Compliance System running on http://localhost:${PORT}`);
    console.log(`📊 Compliance checking, KYC processing, and regulatory monitoring active`);
  });
}

main().catch(console.log);
